using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Core.Utilities.Security.CompanyContext
{
    public class CompanyContext : ICompanyContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<CompanyContext> _logger;

        public CompanyContext(IHttpContextAccessor httpContextAccessor, ILogger<CompanyContext> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public int GetCompanyId()
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext == null)
                {
                    _logger.LogWarning("HttpContext is null in CompanyContext.GetCompanyId()");
                    return -1;
                }

                var user = httpContext.User;
                if (user == null)
                {
                    _logger.LogWarning("User is null in HttpContext");
                    return -1;
                }

                var claims = user.Claims?.ToList();
                if (claims == null || !claims.Any())
                {
                    _logger.LogWarning("No claims found in user token");
                    return -1;
                }

                // Debug: Tüm claim'leri logla
                _logger.LogInformation("Available claims: {Claims}",
                    string.Join(", ", claims.Select(c => $"{c.Type}={c.Value}")));

                var companyIdClaim = claims.FirstOrDefault(c => c.Type == "CompanyId");

                if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int companyId))
                {
                    _logger.LogInformation("CompanyId found in token: {CompanyId}", companyId);
                    return companyId;
                }

                _logger.LogWarning("CompanyId claim not found or invalid. Available claims: {ClaimTypes}",
                    string.Join(", ", claims.Select(c => c.Type)));
                return -1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting CompanyId from token");
                return -1;
            }
        }
    }
}
